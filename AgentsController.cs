using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using IDFCAgentAPI.DTOs;
using IDFCAgentAPI.Models;
using IDFCAgentAPI.Services;
using System.Security.Claims;

namespace IDFCAgentAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AgentsController : ControllerBase
    {
        private readonly IAgentService _agentService;

        public AgentsController(IAgentService agentService)
        {
            _agentService = agentService;
        }

        /// <summary>
        /// Register a new agent
        /// </summary>
        [HttpPost("register")]
        public async Task<ActionResult<AgentResponseDto>> RegisterAgent([FromBody] AgentRegistrationDto registrationDto)
        {
            try
            {
                var agent = await _agentService.RegisterAgentAsync(registrationDto);
                return CreatedAtAction(nameof(GetAgent), new { id = agent.AgentId }, agent);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while registering the agent", details = ex.Message });
            }
        }

        /// <summary>
        /// Get agent by ID
        /// </summary>
        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<AgentResponseDto>> GetAgent(int id)
        {
            var agent = await _agentService.GetAgentByIdAsync(id);
            if (agent == null)
                return NotFound(new { message = "Agent not found" });

            // Check if user can access this agent's data
            var currentUserId = GetCurrentUserId();
            var currentUserRole = GetCurrentUserRole();
            var currentAgentId = GetCurrentAgentId();

            if (currentUserRole == "Agent" && currentAgentId != id)
                return Forbid("You can only access your own data");

            return Ok(agent);
        }

        /// <summary>
        /// Get all agents (Admin/Reviewer only)
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult<IEnumerable<AgentResponseDto>>> GetAllAgents()
        {
            var agents = await _agentService.GetAllAgentsAsync();
            return Ok(agents);
        }

        /// <summary>
        /// Get agents by status (Admin/Reviewer only)
        /// </summary>
        [HttpGet("status/{status}")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult<IEnumerable<AgentResponseDto>>> GetAgentsByStatus(AgentStatus status)
        {
            var agents = await _agentService.GetAgentsByStatusAsync(status);
            return Ok(agents);
        }

        /// <summary>
        /// Update agent information
        /// </summary>
        [HttpPut("{id}")]
        [Authorize]
        public async Task<ActionResult<AgentResponseDto>> UpdateAgent(int id, [FromBody] AgentUpdateDto updateDto)
        {
            // Check if user can update this agent's data
            var currentUserRole = GetCurrentUserRole();
            var currentAgentId = GetCurrentAgentId();

            if (currentUserRole == "Agent" && currentAgentId != id)
                return Forbid("You can only update your own data");

            try
            {
                var updatedAgent = await _agentService.UpdateAgentAsync(id, updateDto);
                if (updatedAgent == null)
                    return NotFound(new { message = "Agent not found" });

                return Ok(updatedAgent);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An error occurred while updating the agent", details = ex.Message });
            }
        }

        /// <summary>
        /// Update agent status (Admin/Reviewer only)
        /// </summary>
        [HttpPatch("{id}/status")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult> UpdateAgentStatus(int id, [FromBody] UpdateStatusDto statusDto)
        {
            var currentUsername = GetCurrentUsername();
            var success = await _agentService.UpdateAgentStatusAsync(id, statusDto.Status, currentUsername, statusDto.Comments);
            
            if (!success)
                return NotFound(new { message = "Agent not found" });

            return Ok(new { message = "Agent status updated successfully" });
        }

        /// <summary>
        /// Get agent status history
        /// </summary>
        [HttpGet("{id}/status-history")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<AgentStatusHistory>>> GetAgentStatusHistory(int id)
        {
            // Check if user can access this agent's data
            var currentUserRole = GetCurrentUserRole();
            var currentAgentId = GetCurrentAgentId();

            if (currentUserRole == "Agent" && currentAgentId != id)
                return Forbid("You can only access your own data");

            var history = await _agentService.GetAgentStatusHistoryAsync(id);
            return Ok(history);
        }

        /// <summary>
        /// Delete agent (Admin only)
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> DeleteAgent(int id)
        {
            var success = await _agentService.DeleteAgentAsync(id);
            if (!success)
                return NotFound(new { message = "Agent not found" });

            return Ok(new { message = "Agent deleted successfully" });
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        private string GetCurrentUserRole()
        {
            return User.FindFirst(ClaimTypes.Role)?.Value ?? "";
        }

        private int? GetCurrentAgentId()
        {
            var agentIdClaim = User.FindFirst("AgentId")?.Value;
            return int.TryParse(agentIdClaim, out var agentId) ? agentId : null;
        }

        private string GetCurrentUsername()
        {
            return User.FindFirst(ClaimTypes.Name)?.Value ?? "";
        }
    }

    public class UpdateStatusDto
    {
        public AgentStatus Status { get; set; }
        public string? Comments { get; set; }
    }
}
