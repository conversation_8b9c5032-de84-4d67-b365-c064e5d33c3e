# IDFC Landing Page - Deployment Instructions

## 📦 Build Package
Use the file: `IDFC-build-enhanced-validation.zip`

## 🚀 Deployment Steps

### 1. Extract and Upload
1. Extract `IDFC-build-enhanced-validation.zip`
2. Upload all contents to your IIS server's `IDFClanding` directory
3. Ensure the folder structure is: `hitesh7672.com/IDFClanding/`

### 2. Configure API URL
After deployment, you can easily change the API URL without rebuilding:

1. Navigate to: `hitesh7672.com/IDFClanding/app.config.json`
2. Edit the file to update the API_URL:
   ```json
   {
     "API_URL": "https://your-production-api-url.com/api/Kyc"
   }
   ```
3. Save the file
4. Refresh your website - changes take effect immediately!

**Important**:
- The application relies entirely on the `app.config.json` file for the API URL
- There are no hardcoded fallback URLs in the code
- Only one config file exists (in the build folder) - no duplicates in source code

### 3. Sample Production Config
A sample production config file `app.config.production.json` is provided with:
```json
{
  "API_URL": "https://hitesh7672.com/api/Kyc"
}
```

You can copy this content to replace the `app.config.json` in your deployed build.

## 🔧 IIS Configuration (Optional)
If your React app uses client-side routing, add this `web.config` file to your `IDFClanding` directory:

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="React Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

## ✅ Verification
After deployment, verify:
1. Website loads at: `https://hitesh7672.com/IDFClanding/`
2. Config loads properly (check browser console for any errors)
3. Form submission works with your API

## 🔄 Future Updates
To change API URL in the future:
1. No need to rebuild the project
2. Simply edit `app.config.json` on your server
3. Changes are applied immediately on page refresh

## 📁 Build Contents
- `index.html` - Main application file with IDFC branding
- `app.config.json` - **Editable configuration file**
- `TitleLogo.png` - **IDFC title logo used as favicon and app icon**
- `static/` - Optimized CSS and JavaScript files
- `manifest.json` - Updated with IDFC branding
- Other assets (favicon.ico, logo192.png, logo512.png, robots.txt)

## 🎨 Branding Updates
- **Browser Tab**: Shows TitleLogo.png as favicon (clear, crisp display)
- **Page Title**: "IDFC FIRST Bank"
- **App Name**: "IDFC KYC" (short name)
- **Description**: "IDFC FIRST Bank - Know Your Customer (KYC) Form"
- **PWA Icons**: All use TitleLogo.png for consistent branding
- **Multiple Sizes**: Optimized for 16x16, 32x32, and 180x180 pixel displays
