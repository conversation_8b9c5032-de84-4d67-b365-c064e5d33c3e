:root {
    --idfc-red: #9d1d27;
    --light-grey-bg: #F8F8F8;
    --form-bg: #FFFFFF;
    --text-color: #4A4A4A;
    --label-color: #666666;
    --border-color: #E0E0E0;
    --consent-bg: #FFF9E6;
    --consent-border: #FFECB3;
}

body {
    font-family: Arial, sans-serif;
    background-color: var(--light-grey-bg);
    margin: 0;
    color: var(--text-color);
}

.main-header {
    background-color: #9d1d27;
    padding: 15px 5%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.main-header .logo {
    width: 150px;
    height: auto;
    display: block;
}

.page-container {
    display: flex;
    justify-content: center;
    padding: 40px 20px;
}

.form-container {
    background: var(--form-bg);
    padding: 40px 60px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    width: 100%;
    max-width: 900px;
}

.form-title {
    color: var(--idfc-red);
    text-align: center;
    margin-bottom: 30px;
    font-weight: normal;
}

.form-section {
    margin-bottom: 30px;
}

.section-title {
    color: var(--idfc-red);
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label, .form-row label {
    display: block;
    margin-bottom: 8px;
    color: var(--label-color);
    font-size: 0.9rem;
}



.required-star {
    color: var(--idfc-red);
}

input[type="text"],
input[type="tel"],
select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 1rem;
}

input::placeholder, select {
    color: #999;
}

.input-group {
    display: flex;
    gap: 15px;
}

.phone-input {
    display: flex;
}

.phone-prefix {
    display: flex;
    align-items: center;
    padding: 0 15px;
    background-color: #f7f7f7;
    border: 1px solid var(--border-color);
    border-right: none;
    border-radius: 4px 0 0 4px;
}

.phone-input input {
    border-radius: 0 4px 4px 0;
}

.field-note {
    font-size: 0.8rem;
    color: #888;
    display: block;
    margin-top: 5px;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

.consent-box {
    background-color: var(--consent-bg);
    border: 1px solid var(--consent-border);
    padding: 20px;
    border-radius: 4px;
}

.consent-box p {
    margin: 0 0 15px 0;
    line-height: 1.6;
    font-size: 0.9rem;
}

.declaration {
    display: flex;
    align-items: center;
}

.declaration input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
}

.declaration label {
    margin: 0;
    font-size: 0.9rem;
}

.submit-container {
    text-align: center;
    margin-top: 30px;
}

.form-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    padding: 20px 0;
    width: 100%;
}

.submit-btn {
    background-color: var(--idfc-red);
    color: white;
    padding: 12px 40px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: bold;
    transition: background-color 0.3s;
    min-width: 120px;
}

.submit-btn:hover {
    background-color: #7a1620;
}

.error-message {
    color: #9d1d27;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
}

@media (max-width: 768px) {
    .form-container {
        padding: 30px;
    }
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    .input-group {
        flex-direction: column;
    }

    .form-actions {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
        padding: 20px 15px;
        width: 100%;
        text-align: center;
    }

    .submit-btn {
        width: 100%;
        max-width: 300px;
        padding: 15px 20px;
        font-size: 1.1rem;
        margin: 0 auto;
    }
}