import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './KycForm.css';
import idfcLogo from '../assets/idfc-logo-compact.png';
import locations from '../data/locations.json';
import ThankYou from './ThankYou';

const KycForm = () => {
    const [config, setConfig] = useState(null);
    const [formData, setFormData] = useState({
        firstName: '',
        middleName: '',
        lastName: '',
        phoneNumber: '',
        alternatePhoneNumber: '',
        vehicleRegistrationNumber: '',
        addressLine1: '',
        addressLine2: '',
        addressLine3: '',
        landmark: '',
        city: '',
        district: '',
        state: '',
        pinCode: '',
        declarationAccepted: false,
    });

    const [districts, setDistricts] = useState([]);
    const [cities, setCities] = useState([]);

    const [formStatus, setFormStatus] = useState({
        submitting: false,
        submitted: false,
        error: null,
    });

    const [nameErrors, setNameErrors] = useState({ firstName: '', middleName: '', lastName: '' });
    const [phoneErrors, setPhoneErrors] = useState({ phoneNumber: '', alternatePhoneNumber: '' });
    const [pinCodeError, setPinCodeError] = useState('');
    const [vehicleRegError, setVehicleRegError] = useState('');

    // Load configuration from public folder
    useEffect(() => {
        const loadConfig = async () => {
            try {
                // Dynamic config URL based on environment
                const baseUrl = process.env.NODE_ENV === 'production'
                    ? `${window.location.origin}${process.env.PUBLIC_URL || ''}`
                    : '';
                const configUrl = `${baseUrl}/app.config.json`;

                console.log('Loading config from:', configUrl);
                const response = await fetch(configUrl);
                if (!response.ok) {
                    throw new Error(`Failed to fetch config: ${response.status}`);
                }
                const configData = await response.json();
                console.log('Config loaded successfully:', configData);
                setConfig(configData);
            } catch (error) {
                console.error('Failed to load configuration:', error);
                // Fallback config so the app still works
                const fallbackConfig = {
                    API_URL: process.env.NODE_ENV === 'production'
                        ? `${window.location.origin}/api/Kyc`
                        : 'https://localhost:7098/api/Kyc'
                };
                console.log('Using fallback config:', fallbackConfig);
                setConfig(fallbackConfig);
            }
        };
        loadConfig();
    }, []);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        // Name validation
        if (["firstName", "middleName", "lastName"].includes(name)) {
            const valid = /^[A-Za-z ]*$/.test(value);
            if (!valid) {
                setNameErrors(prev => ({ ...prev, [name]: 'Only letters and spaces are allowed.' }));
                return;
            } else {
                setNameErrors(prev => ({ ...prev, [name]: '' }));
            }
        }
        // Phone validation
        if (["phoneNumber", "alternatePhoneNumber"].includes(name)) {
            const valid = /^\d*$/.test(value);
            if (!valid) {
                setPhoneErrors(prev => ({ ...prev, [name]: 'Only numbers are allowed.' }));
                return;
            } else {
                setPhoneErrors(prev => ({ ...prev, [name]: '' }));
            }
        }
        // PIN Code validation
        if (name === "pinCode") {
            const valid = /^\d*$/.test(value);
            if (!valid) {
                setPinCodeError('Only numbers are allowed.');
                return;
            } else if (value.length > 6) {
                setPinCodeError('PIN Code must be exactly 6 digits.');
                return;
            } else if (value.length > 0 && value.length < 6) {
                setPinCodeError('PIN Code must be exactly 6 digits.');
            } else {
                setPinCodeError('');
            }
        }
        // Vehicle Registration Number validation
        if (name === "vehicleRegistrationNumber") {
            const valid = /^[A-Za-z0-9-]*$/.test(value);
            if (!valid) {
                setVehicleRegError('Only alphanumeric characters and hyphens are allowed.');
                return;
            } else {
                setVehicleRegError('');
            }
        }
        setFormData(prevState => ({
            ...prevState,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleStateChange = (e) => {
        const stateName = e.target.value;
        const stateData = locations.find(s => s.name === stateName);
        setFormData(prevState => ({
            ...prevState,
            state: stateName,
            district: '',
            city: ''
        }));
        setDistricts(stateData ? stateData.districtList : []);
        setCities([]);
    };

    const handleDistrictChange = (e) => {
        const districtName = e.target.value;
        const stateData = locations.find(s => s.name === formData.state);
        const districtData = stateData?.districtList.find(d => d.name === districtName);
        setFormData(prevState => ({
            ...prevState,
            district: districtName,
            city: ''
        }));
        setCities(districtData ? districtData.blockList : []);
    };

    const handleSubmit = async (e) => {
        debugger;
        e.preventDefault();
        setFormStatus({ submitting: true, submitted: false, error: null });

        if (!formData.declarationAccepted) {
            setFormStatus({
                submitting: false,
                submitted: false,
                error: 'You must accept the declaration to proceed.',
            });
            return;
        }

        // Check if config is loaded (should always be available due to fallback)
        if (!config || !config.API_URL) {
            setFormStatus({
                submitting: false,
                submitted: false,
                error: 'Unable to determine API endpoint. Please refresh the page and try again.'
            });
            return;
        }

        try {
            debugger;
            const response = await axios.post(config.API_URL, formData);
            debugger;
            if (response.status === 201) {
                setFormStatus({ submitting: false, submitted: true, error: null });
            }
        } catch (error) {
            let errorMessage = 'An unexpected error occurred.';
            if (error.response) {
                console.error('Error response:', error.response.data);
                errorMessage = 'Please check your form for errors and try again.';
            } else if (error.request) {
                console.error('Error request:', error.request);
                errorMessage = 'Could not connect to the server. Please try again later.';
            } else {
                console.error('Error message:', error.message);
            }
            setFormStatus({ submitting: false, submitted: false, error: errorMessage });
        }
    };

    if (formStatus.submitted) {
        return <ThankYou />;
    }

    return (
        <>
            <header className="main-header">
                <img src={idfcLogo} alt="IDFC FIRST Bank Logo" className="logo" />
            </header>
            <div className="page-container">
                <div className="form-container">
                    <h1 className="form-title">Know Your Customer (KYC)                    </h1>
                    <form onSubmit={handleSubmit}>
                        <section className="form-section">
                            <h2 className="section-title">Personal Information</h2>
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">First Name *</label>
                                    <input type="text" id="firstName" name="firstName" value={formData.firstName} onChange={handleChange} required maxLength={50} />
                                    {nameErrors.firstName && <div className="error-message">{nameErrors.firstName}</div>}
                                </div>
                                <div className="form-group">
                                    <label htmlFor="middleName">Middle Name</label>
                                    <input type="text" id="middleName" name="middleName" value={formData.middleName} onChange={handleChange} maxLength={40} />
                                    {nameErrors.middleName && <div className="error-message">{nameErrors.middleName}</div>}
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">Last Name *</label>
                                    <input type="text" id="lastName" name="lastName" value={formData.lastName} onChange={handleChange} required maxLength={40} />
                                    {nameErrors.lastName && <div className="error-message">{nameErrors.lastName}</div>}
                                </div>
                            </div>
                             <div className="form-group">
                                <label>Phone Number <span className="required-star">*</span></label>
                                <div className="phone-input">
                                    <span className="phone-prefix">+91</span>
                                    <input type="tel" name="phoneNumber" placeholder="Enter phone number" value={formData.phoneNumber} onChange={handleChange} required pattern="[0-9]{10}" title="Please enter a valid 10-digit phone number" maxLength={10}/>
                                </div>
                                {phoneErrors.phoneNumber && <div className="error-message">{phoneErrors.phoneNumber}</div>}
                            </div>
                             <div className="form-group">
                                <label>Alternate Phone Number</label>
                                <input type="tel" name="alternatePhoneNumber" placeholder="Enter alternate phone number" value={formData.alternatePhoneNumber} onChange={handleChange} pattern="[0-9]{10}" title="Please enter a valid 10-digit phone number" maxLength={2}/>
                                <small className="field-note">Enter if Aadhaar is linked to different number</small>
                                {phoneErrors.alternatePhoneNumber && <div className="error-message">{phoneErrors.alternatePhoneNumber}</div>}
                            </div>
                        </section>

                        <section className="form-section">
                            <h2 className="section-title">Vehicle Details</h2>
                            <div className="form-group">
                                <label>Vehicle Registration Number <span className="required-star">*</span></label>
                                <input type="text" name="vehicleRegistrationNumber" placeholder="XX-XX-XX-XXXX" value={formData.vehicleRegistrationNumber} onChange={handleChange} required maxLength={15} />
                                {vehicleRegError && <div className="error-message">{vehicleRegError}</div>}
                            </div>
                        </section>

                        <section className="form-section">
                             <h2 className="section-title">Address Details</h2>
                             <div className="form-group">
                                <label>Address Line 1 <span className="required-star">*</span></label>
                                <input type="text" name="addressLine1" placeholder="Enter address line 1" value={formData.addressLine1} onChange={handleChange} required maxLength={200} />
                            </div>
                            <div className="form-group">
                                <label>Address Line 2</label>
                                <input type="text" name="addressLine2" placeholder="Enter address line 2" value={formData.addressLine2} onChange={handleChange} maxLength={200} />
                            </div>
                            <div className="form-group">
                                <label>Address Line 3</label>
                                <input type="text" name="addressLine3" placeholder="Enter address line 3" value={formData.addressLine3} onChange={handleChange} maxLength={200} />
                            </div>
                            <div className="form-group">
                                <label>Landmark</label>
                                <input type="text" name="landmark" placeholder="Enter landmark" value={formData.landmark} onChange={handleChange} maxLength={50}/>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    <label>PIN Code <span className="required-star">*</span></label>
                                    <input type="text" name="pinCode" placeholder="Enter PIN Code" value={formData.pinCode} onChange={handleChange} required pattern="[0-9]{6}" title="Please enter a valid 6-digit PIN code" maxLength={6}/>
                                </div>
                                {pinCodeError && <div className="error-message">{pinCodeError}</div>}
                                <div className="form-group">
                                     <label>State <span className="required-star">*</span></label>
                                     <select name="state" value={formData.state} onChange={handleStateChange} required>
                                        <option value="">Select State</option>
                                        {locations.map(s => <option key={s.name} value={s.name}>{s.name}</option>)}
                                     </select>
                                </div>
                            </div>
                             <div className="form-row">
                                <div className="form-group">
                                     <label>District <span className="required-star">*</span></label>
                                     <select name="district" value={formData.district} onChange={handleDistrictChange} required disabled={!formData.state}>
                                        <option value="">Select District</option>
                                        {districts.map(d => <option key={d.name} value={d.name}>{d.name}</option>)}
                                     </select>
                                </div>
                                <div className="form-group">
                                     <label>City <span className="required-star">*</span></label>
                                     <select name="city" value={formData.city} onChange={handleChange} required disabled={!formData.district}>
                                        <option value="">Select City</option>
                                        {cities.map(c => <option key={c} value={c}>{c}</option>)}
                                     </select>
                                </div>
                            </div>
                        </section>

                        <section className="form-section consent-section">
                            <h2 className="section-title">Declaration & Consent</h2>
                            <div className="consent-box">
                                <p>By clicking 'Proceed', I consent to complete my IDFC FIRST Bank FASTag KYC through Manipal Business Solutions (MBS), an independent third party. I authorize MBS to contact me on my mobile number for this purpose and consent to my information being stored on MBS's servers for processing and presentation. I acknowledge that IDFC FIRST Bank is not liable for any issues arising from the KYC process conducted by MBS.</p>
                                <div className="declaration">
                                    <input type="checkbox" id="declaration" name="declarationAccepted" checked={formData.declarationAccepted} onChange={handleChange} required />
                                    <label htmlFor="declaration">I agree to the above declaration and consent to the processing of my personal information as described above</label>
                                </div>
                            </div>
                        </section>

                        {formStatus.error && <p className="error-message">{formStatus.error}</p>}
                        
                        <div className="form-actions">
                            <button type="submit" className="submit-btn" disabled={formStatus.submitting}>
                                {formStatus.submitting ? 'Submitting...' : 'Submit'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </>
    );
};

export default KycForm;