import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './KycForm.css';
import idfcLogo from '../assets/idfc-logo-compact.png';
import locations from '../data/locations.json';
import ThankYou from './ThankYou';

const KycForm = () => {
    const [config, setConfig] = useState(null);
    const [formData, setFormData] = useState({
        firstName: '',
        middleName: '',
        lastName: '',
        phoneNumber: '',
        alternatePhoneNumber: '',
        vehicleRegistrationNumber: '',
        addressLine1: '',
        addressLine2: '',
        addressLine3: '',
        landmark: '',
        city: '',
        district: '',
        state: '',
        pinCode: '',
        declarationAccepted: false,
    });

    const [districts, setDistricts] = useState([]);
    const [cities, setCities] = useState([]);

    const [formStatus, setFormStatus] = useState({
        submitting: false,
        submitted: false,
        error: null,
    });

    const [nameErrors, setNameErrors] = useState({ firstName: '', middleName: '', lastName: '' });
    const [phoneErrors, setPhoneErrors] = useState({ phoneNumber: '', alternatePhoneNumber: '' });
    const [pinCodeError, setPinCodeError] = useState('');
    const [vehicleRegError, setVehicleRegError] = useState('');

    // Load configuration from public folder
    useEffect(() => {
        const loadConfig = async () => {
            try {
                // Dynamic config URL based on environment
                const baseUrl = process.env.NODE_ENV === 'production'
                    ? `${window.location.origin}${process.env.PUBLIC_URL || ''}`
                    : '';
                const configUrl = `${baseUrl}/app.config.json`;

                console.log('Loading config from:', configUrl);
                const response = await fetch(configUrl);
                if (!response.ok) {
                    throw new Error(`Failed to fetch config: ${response.status}`);
                }
                const configData = await response.json();
                console.log('Config loaded successfully:', configData);
                setConfig(configData);
            } catch (error) {
                console.error('Failed to load configuration:', error);
                // Fallback config so the app still works
                const fallbackConfig = {
                    API_URL: process.env.NODE_ENV === 'production'
                        ? `${window.location.origin}/api/Kyc`
                        : 'https://localhost:7098/api/Kyc'
                };
                console.log('Using fallback config:', fallbackConfig);
                setConfig(fallbackConfig);
            }
        };
        loadConfig();
    }, []);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;

        // Trim spaces for text inputs and validate
        let processedValue = value;
        if (type !== 'checkbox' && type !== 'select-one') {
            processedValue = value.trimStart(); // Remove leading spaces while typing
        }

        // Name validation
        if (["firstName", "middleName", "lastName"].includes(name)) {
            const valid = /^[A-Za-z ]*$/.test(processedValue);
            if (!valid) {
                setNameErrors(prev => ({ ...prev, [name]: 'Only letters and spaces are allowed.' }));
                return;
            } else {
                setNameErrors(prev => ({ ...prev, [name]: '' }));
            }
        }
        // Phone validation
        if (["phoneNumber", "alternatePhoneNumber"].includes(name)) {
            const valid = /^\d*$/.test(processedValue);
            if (!valid) {
                setPhoneErrors(prev => ({ ...prev, [name]: 'Only numbers are allowed.' }));
                return;
            } else if (processedValue.length > 0 && !/^[6789]/.test(processedValue)) {
                setPhoneErrors(prev => ({ ...prev, [name]: 'Mobile number must start with 6, 7, 8, or 9.' }));
            } else if (processedValue.length > 0 && processedValue.length < 10) {
                setPhoneErrors(prev => ({ ...prev, [name]: 'Mobile number must be 10 digits.' }));
            } else if (processedValue.length > 10) {
                setPhoneErrors(prev => ({ ...prev, [name]: 'Mobile number cannot exceed 10 digits.' }));
                return;
            } else {
                setPhoneErrors(prev => ({ ...prev, [name]: '' }));
            }
        }
        // PIN Code validation
        if (name === "pinCode") {
            const valid = /^\d*$/.test(value);
            if (!valid) {
                setPinCodeError('Only numbers are allowed.');
                return;
            } else if (value.length > 6) {
                setPinCodeError('PIN Code must be exactly 6 digits.');
                return;
            } else if (value.length > 0 && value.length < 6) {
                setPinCodeError('PIN Code must be exactly 6 digits.');
            } else {
                setPinCodeError('');
            }
        }
        // Vehicle Registration Number validation
        if (name === "vehicleRegistrationNumber") {
            const valid = /^[A-Za-z0-9]*$/.test(processedValue);
            if (!valid) {
                setVehicleRegError('Only alphanumeric characters are allowed.');
                return;
            } else if (processedValue.length > 0 && processedValue.length < 10) {
                setVehicleRegError('Vehicle Registration Number must be at least 10 characters.');
            } else if (processedValue.length > 15) {
                setVehicleRegError('Vehicle Registration Number cannot exceed 15 characters.');
                return;
            } else {
                setVehicleRegError('');
            }
        }

        // General validation for text fields - prevent only spaces
        const textFields = ['addressLine1', 'addressLine2', 'addressLine3', 'landmark'];
        if (textFields.includes(name) && processedValue.trim() === '' && processedValue.length > 0) {
            // If field has content but only spaces, clear it
            processedValue = '';
        }
        setFormData(prevState => ({
            ...prevState,
            [name]: type === 'checkbox' ? checked : processedValue
        }));
    };

    const handleStateChange = (e) => {
        const stateName = e.target.value;
        const stateData = locations.find(s => s.name === stateName);
        setFormData(prevState => ({
            ...prevState,
            state: stateName,
            district: '',
            city: ''
        }));
        setDistricts(stateData ? stateData.districtList : []);
        setCities([]);
    };

    const handleDistrictChange = (e) => {
        const districtName = e.target.value;
        const stateData = locations.find(s => s.name === formData.state);
        const districtData = stateData?.districtList.find(d => d.name === districtName);
        setFormData(prevState => ({
            ...prevState,
            district: districtName,
            city: ''
        }));
        setCities(districtData ? districtData.blockList : []);
    };

    const handleSubmit = async (e) => {
        debugger;
        e.preventDefault();
        setFormStatus({ submitting: true, submitted: false, error: null });

        // Validate required fields for empty/whitespace-only values
        const requiredFields = {
            firstName: 'First Name',
            phoneNumber: 'Phone Number',
            vehicleRegistrationNumber: 'Vehicle Registration Number',
            addressLine1: 'Address Line 1',
            state: 'State',
            district: 'District',
            pinCode: 'PIN Code'
        };

        for (const [field, label] of Object.entries(requiredFields)) {
            if (!formData[field] || formData[field].toString().trim() === '') {
                setFormStatus({
                    submitting: false,
                    submitted: false,
                    error: `${label} is required and cannot be empty or contain only spaces.`
                });
                return;
            }
        }

        // Additional validation for specific field formats
        // Phone number validation
        const phoneNumber = formData.phoneNumber ? formData.phoneNumber.trim() : '';
        if (phoneNumber) {
            // Check if phone number contains only digits
            if (!/^\d+$/.test(phoneNumber)) {
                setFormStatus({
                    submitting: false,
                    submitted: false,
                    error: 'Phone number must contain only digits.'
                });
                return;
            }
            // Check length
            if (phoneNumber.length !== 10) {
                setFormStatus({
                    submitting: false,
                    submitted: false,
                    error: 'Phone number must be exactly 10 digits.'
                });
                return;
            }
            // Check if it starts with 6, 7, 8, or 9
            if (!/^[6789]/.test(phoneNumber)) {
                setFormStatus({
                    submitting: false,
                    submitted: false,
                    error: 'Phone number must start with 6, 7, 8, or 9.'
                });
                return;
            }
        }

        // Alternate phone number validation (if provided)
        const alternatePhoneNumber = formData.alternatePhoneNumber ? formData.alternatePhoneNumber.trim() : '';
        if (alternatePhoneNumber && alternatePhoneNumber !== '') {
            // Check if alternate phone number contains only digits
            if (!/^\d+$/.test(alternatePhoneNumber)) {
                setFormStatus({
                    submitting: false,
                    submitted: false,
                    error: 'Alternate phone number must contain only digits.'
                });
                return;
            }
            // Check length
            if (alternatePhoneNumber.length !== 10) {
                setFormStatus({
                    submitting: false,
                    submitted: false,
                    error: 'Alternate phone number must be exactly 10 digits.'
                });
                return;
            }
            // Check if it starts with 6, 7, 8, or 9
            if (!/^[6789]/.test(alternatePhoneNumber)) {
                setFormStatus({
                    submitting: false,
                    submitted: false,
                    error: 'Alternate phone number must start with 6, 7, 8, or 9.'
                });
                return;
            }
        }

        // Vehicle registration number validation
        if (formData.vehicleRegistrationNumber && (formData.vehicleRegistrationNumber.length < 10 || formData.vehicleRegistrationNumber.length > 15)) {
            setFormStatus({
                submitting: false,
                submitted: false,
                error: 'Vehicle Registration Number must be between 10 and 15 characters.'
            });
            return;
        }

        if (!formData.declarationAccepted) {
            setFormStatus({
                submitting: false,
                submitted: false,
                error: 'You must accept the declaration to proceed.',
            });
            return;
        }

        // Check if config is loaded (should always be available due to fallback)
        if (!config || !config.API_URL) {
            setFormStatus({
                submitting: false,
                submitted: false,
                error: 'Unable to determine API endpoint. Please refresh the page and try again.'
            });
            return;
        }

        try {
            // Clean form data by trimming all text fields
            const cleanedFormData = { ...formData };
            Object.keys(cleanedFormData).forEach(key => {
                if (typeof cleanedFormData[key] === 'string') {
                    cleanedFormData[key] = cleanedFormData[key].trim();
                }
            });

            debugger;
            const response = await axios.post(config.API_URL, cleanedFormData);
            debugger;
            if (response.status === 201) {
                setFormStatus({ submitting: false, submitted: true, error: null });
            }
        } catch (error) {
            let errorMessage = 'An unexpected error occurred.';
            if (error.response) {
                console.error('Error response:', error.response.data);
                errorMessage = 'Please check your form for errors and try again.';
            } else if (error.request) {
                console.error('Error request:', error.request);
                errorMessage = 'Could not connect to the server. Please try again later.';
            } else {
                console.error('Error message:', error.message);
            }
            setFormStatus({ submitting: false, submitted: false, error: errorMessage });
        }
    };

    if (formStatus.submitted) {
        return <ThankYou />;
    }

    return (
        <>
            <header className="main-header">
                <img src={idfcLogo} alt="IDFC FIRST Bank Logo" className="logo" />
            </header>
            <div className="page-container">
                <div className="form-container">
                    <h1 className="form-title">Know Your Customer (KYC)                    </h1>
                    <form onSubmit={handleSubmit}>
                        <section className="form-section">
                            <h2 className="section-title">Personal Information</h2>
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">First Name <span className="required-star">*</span></label>
                                    <input type="text" id="firstName" name="firstName" value={formData.firstName} onChange={handleChange} required maxLength={50} />
                                    {nameErrors.firstName && <div className="error-message">{nameErrors.firstName}</div>}
                                </div>
                                <div className="form-group">
                                    <label htmlFor="middleName">Middle Name</label>
                                    <input type="text" id="middleName" name="middleName" value={formData.middleName} onChange={handleChange} maxLength={50} />
                                    {nameErrors.middleName && <div className="error-message">{nameErrors.middleName}</div>}
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">Last Name</label>
                                    <input type="text" id="lastName" name="lastName" value={formData.lastName} onChange={handleChange} maxLength={50} />
                                    {nameErrors.lastName && <div className="error-message">{nameErrors.lastName}</div>}
                                </div>
                            </div>
                             <div className="form-group">
                                <label>Phone Number <span className="required-star">*</span></label>
                                <div className="phone-input">
                                    <span className="phone-prefix">+91</span>
                                    <input type="tel" name="phoneNumber" placeholder="Enter phone number" value={formData.phoneNumber} onChange={handleChange} required pattern="[0-9]{10}" title="Please enter a valid 10-digit phone number" maxLength={10}/>
                                </div>
                                {phoneErrors.phoneNumber && <div className="error-message">{phoneErrors.phoneNumber}</div>}
                            </div>
                             <div className="form-group">
                                <label>Alternate Phone Number</label>
                                <input type="tel" name="alternatePhoneNumber" placeholder="Enter alternate phone number" value={formData.alternatePhoneNumber} onChange={handleChange} pattern="[0-9]{10}" title="Please enter a valid 10-digit phone number" maxLength={10}/>
                                <small className="field-note">Enter if Aadhaar is linked to different number</small>
                                {phoneErrors.alternatePhoneNumber && <div className="error-message">{phoneErrors.alternatePhoneNumber}</div>}
                            </div>
                        </section>

                        <section className="form-section">
                            <h2 className="section-title">Vehicle Details</h2>
                            <div className="form-group">
                                <label>Vehicle Registration Number <span className="required-star">*</span></label>
                                <input type="text" name="vehicleRegistrationNumber" placeholder="XXXXXXXXXX" value={formData.vehicleRegistrationNumber} onChange={handleChange} required maxLength={15} />
                                {vehicleRegError && <div className="error-message">{vehicleRegError}</div>}
                            </div>
                        </section>

                        <section className="form-section">
                             <h2 className="section-title">Address Details</h2>
                             <div className="form-group">
                                <label>Address Line 1 <span className="required-star">*</span></label>
                                <input type="text" name="addressLine1" placeholder="Enter address line 1" value={formData.addressLine1} onChange={handleChange} required maxLength={200} />
                            </div>
                            <div className="form-group">
                                <label>Address Line 2</label>
                                <input type="text" name="addressLine2" placeholder="Enter address line 2" value={formData.addressLine2} onChange={handleChange} maxLength={200} />
                            </div>
                            <div className="form-group">
                                <label>Address Line 3</label>
                                <input type="text" name="addressLine3" placeholder="Enter address line 3" value={formData.addressLine3} onChange={handleChange} maxLength={200} />
                            </div>
                            <div className="form-group">
                                <label>Landmark</label>
                                <input type="text" name="landmark" placeholder="Enter landmark" value={formData.landmark} onChange={handleChange} maxLength={50}/>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    <label>PIN Code <span className="required-star">*</span></label>
                                    <input type="text" name="pinCode" placeholder="Enter PIN Code" value={formData.pinCode} onChange={handleChange} required pattern="[0-9]{6}" title="Please enter a valid 6-digit PIN code" maxLength={6}/>
                                </div>
                                {pinCodeError && <div className="error-message">{pinCodeError}</div>}
                                <div className="form-group">
                                     <label>State <span className="required-star">*</span></label>
                                     <select name="state" value={formData.state} onChange={handleStateChange} required>
                                        <option value="">Select State</option>
                                        {locations.map(s => <option key={s.name} value={s.name}>{s.name}</option>)}
                                     </select>
                                </div>
                                  <div className="form-group">
                                     <label>District/City <span className="required-star">*</span></label>
                                     <select name="district" value={formData.district} onChange={handleDistrictChange} required disabled={!formData.state}>
                                        <option value="">Select District/City</option>
                                        {districts.map(d => <option key={d.name} value={d.name}>{d.name}</option>)}
                                     </select>
                                </div>
                            </div>
                             <div className="form-row" style={{ display: 'none' }}>
                                <div className="form-group" style={{ display: 'none' }}>
                                     <label>City <span className="required-star">*</span></label>
                                     <select name="city" value={formData.city} onChange={handleChange} disabled={!formData.district}>
                                        <option value="">Select City</option>
                                        {cities.map(c => <option key={c} value={c}>{c}</option>)}
                                     </select>
                                </div>
                            </div>
                        </section>

                        <section className="form-section consent-section">
                            <h2 className="section-title">Declaration & Consent</h2>
                            <div className="consent-box">
                                <p>By clicking 'Proceed', I consent to complete my IDFC FIRST Bank FASTag KYC through Manipal Business Solutions (MBS), an independent third party. I authorize MBS to contact me on my mobile number for this purpose and consent to my information being stored on MBS's servers for processing and presentation. I acknowledge that IDFC FIRST Bank is not liable for any issues arising from the KYC process conducted by MBS.</p>
                                <div className="declaration">
                                    <input type="checkbox" id="declaration" name="declarationAccepted" checked={formData.declarationAccepted} onChange={handleChange} required />
                                    <label htmlFor="declaration">I agree to the above declaration and consent to the processing of my personal information as described above</label>
                                </div>
                            </div>
                        </section>

                        {formStatus.error && <p className="error-message">{formStatus.error}</p>}
                        
                        <div className="form-actions">
                            <button type="submit" className="submit-btn" disabled={formStatus.submitting}>
                                {formStatus.submitting ? 'Submitting...' : 'Submit'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </>
    );
};

export default KycForm;